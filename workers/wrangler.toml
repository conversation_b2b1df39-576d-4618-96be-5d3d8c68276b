name = "getgoodtape-api"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Development server configuration
[dev]
port = 8789
local_protocol = "http"

[env.development]
vars = { ENVIRONMENT = "development", PROCESSING_SERVICE_URL = "https://getgoodtape-video-proc.fly.dev" }

[env.production]
vars = { ENVIRONMENT = "production", PROCESSING_SERVICE_URL = "https://getgoodtape-video-proc.fly.dev" }

# D1 Database bindings
[[env.production.d1_databases]]
binding = "DB"
database_name = "getgoodtape-prod"
database_id = "0e89da43-8723-4bf3-bc4c-3bd484cc148e"

[[env.development.d1_databases]]
binding = "DB"
database_name = "getgoodtape-dev"
database_id = "8835135a-9d34-4f5a-8fde-87352d1b57f9"

# R2 Storage bindings
[[env.production.r2_buckets]]
binding = "STORAGE"
bucket_name = "getgoodtape-files"

[[env.development.r2_buckets]]
binding = "STORAGE"
bucket_name = "getgoodtape-files-dev"

# KV namespace bindings
[[env.production.kv_namespaces]]
binding = "CACHE"
id = "cabbed24ed1f42df994bb05b4cdc543a"

[[env.development.kv_namespaces]]
binding = "CACHE"
id = "your-dev-kv-namespace-id"

# Cron triggers for queue processing
[triggers]
crons = ["* * * * *"]  # Run every minute (Cloudflare standard format)